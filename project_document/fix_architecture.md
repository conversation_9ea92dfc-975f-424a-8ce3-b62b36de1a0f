# DocumentPdfViewer.vue 修复架构设计

## 设计时间
2025-08-18T16:47:22+08:00

## 架构原则
- **最小侵入性**: 只修复问题，不重构整体架构
- **向后兼容**: 保持现有API和功能不变
- **性能优先**: 确保修复不影响渲染性能
- **用户体验**: 优化文本选择和搜索交互

## 修复架构

### 1. TextLayer透明度修复架构

#### 问题层次
```
CSS层 (最高优先级)
├── .textLayer { opacity: 0 !important; } ❌ 问题源
└── 内联样式被覆盖

JavaScript层
├── textLayerDiv.style.opacity = '0.2' ✅ 正确设置
└── 被CSS覆盖
```

#### 解决方案架构
```
方案A: 移除CSS !important (推荐)
├── 删除 opacity: 0 !important
├── 保留JavaScript动态设置
└── 允许调试模式切换透明度

方案B: 提升JavaScript优先级
├── 使用 !important 在JavaScript中
├── 或使用更具体的CSS选择器
└── 风险：样式管理复杂化
```

### 2. 搜索输入框诊断架构

#### 诊断流程
```
Level 1: Element Plus配置检查
├── disabled/readonly属性验证
├── v-model绑定状态检查
└── ref引用完整性验证

Level 2: 事件系统检查  
├── 全局事件监听器冲突
├── 键盘事件拦截
└── 输入事件阻止

Level 3: CSS样式检查
├── pointer-events设置
├── z-index层叠问题
└── 输入框可见性
```

#### 修复策略
```
策略1: 渐进式诊断
├── 添加调试日志
├── 逐步排除问题源
└── 最小化修改范围

策略2: 重置输入框状态
├── 清除可能的状态污染
├── 重新初始化组件
└── 验证基础功能
```

### 3. 整体修复流程

#### 阶段1: 紧急修复 (TextLayer透明度)
- 目标：立即恢复文本选择功能
- 方法：移除CSS opacity: 0 !important
- 验证：确认文本可选择

#### 阶段2: 深度诊断 (搜索输入框)
- 目标：找出输入框无法输入的根本原因
- 方法：系统性排查配置、事件、样式
- 验证：确认输入框正常工作

#### 阶段3: 优化完善
- 目标：优化用户体验和性能
- 方法：调整透明度、优化样式
- 验证：整体功能测试

## 技术实现细节

### TextLayer最佳实践配置
```css
.textLayer {
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  overflow: hidden !important;
  opacity: 0.1 !important; /* 修改：可见但不干扰阅读 */
  line-height: 1.0 !important;
  pointer-events: auto !important;
  user-select: text !important;
}
```

### 调试模式支持
```javascript
// 支持动态切换TextLayer可见性用于调试
const debugTextLayer = ref(false)
const textLayerOpacity = computed(() => 
  debugTextLayer.value ? 0.3 : 0.1
)
```

## 风险评估
- **低风险**: CSS透明度修改，影响范围明确
- **中风险**: 搜索输入框问题可能涉及多个系统
- **缓解措施**: 分阶段修复，每步验证功能
